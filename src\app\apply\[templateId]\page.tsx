"use client";

import { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertCircle,
  FileText,
  Upload,
  User,
  Loader2,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

// Layout size dimensions for html2canvas and PDF generation
const LAYOUT_DIMENSIONS = {
  A4: {
    width: 794, // 210mm at 96 DPI
    height: 1123, // 297mm at 96 DPI
    pdfWidth: 210, // mm
    pdfHeight: 297, // mm
  },
  Letter: {
    width: 816, // 8.5in at 96 DPI
    height: 1056, // 11in at 96 DPI
    pdfWidth: 215.9, // mm (8.5 inches)
    pdfHeight: 279.4, // mm (11 inches)
  },
} as const;

interface Template {
  id: number;
  template_name: string;
  description?: string;
  filename: string;
  placeholders: string[];
  layout_size?: string;
  uploaded_at: string;
  user_id: number;
}

interface FormData {
  [key: string]: string | File | null;
}

interface FormErrors {
  [key: string]: string;
}

export default function ApplyForCertificatePage() {
  const params = useParams();
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [template, setTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [submitting, setSubmitting] = useState(false);
  const [hasApplicantPhoto, setHasApplicantPhoto] = useState(false);

  // Form state
  const [formData, setFormData] = useState<FormData>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});

  const templateId = params.templateId as string;

  // Optional fields that don't require validation
  const optionalFields = ["MIDDLE_INITIAL", "SUFFIX"];

  // Fetch template data
  useEffect(() => {
    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const fetchTemplate = async () => {
    try {
      setLoading(true);
      setError("");

      const response = await fetch(`/api/templates/${templateId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch template");
      }

      const templateData: Template = await response.json();
      setTemplate(templateData);

      // Initialize form data with empty values for all placeholders
      const initialFormData: FormData = {};
      templateData.placeholders.forEach((placeholder) => {
        // Auto-populate date fields with current date
        if (isDatePlaceholder(placeholder)) {
          initialFormData[placeholder] = getDateValue(placeholder);
        } else {
          initialFormData[placeholder] = "";
        }
      });
      setFormData(initialFormData);

      // Check if template requires applicant photo by checking template HTML
      await checkForApplicantPhoto(templateData.id);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load template";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const checkForApplicantPhoto = async (templateId: number) => {
    try {
      const response = await fetch(`/api/templates/${templateId}/view`);
      if (response.ok) {
        const htmlContent = await response.text();
        // Check for alt attribute containing "applicants_photo" (with possible HTML entities)
        const hasPhoto = /alt="[^"]*applicants_photo[^"]*"/i.test(htmlContent);
        setHasApplicantPhoto(hasPhoto);

        if (hasPhoto) {
          setFormData((prev) => ({ ...prev, applicants_photo: null }));
        }
      }
    } catch (err) {
      console.error("Failed to check for applicant photo:", err);
    }
  };

  const handleInputChange = (placeholder: string, value: string) => {
    let formattedValue = value;

    // Auto-format based on placeholder type
    if (placeholder === "MIDDLE_INITIAL") {
      // Auto-capitalize and add period for middle initial
      formattedValue = formatMiddleInitial(value);
    } else if (placeholder === "SUFFIX") {
      // Auto-capitalize suffix (e.g., III, II, I, Jr., Sr.)
      formattedValue = formatSuffix(value);
    } else {
      // Auto-capitalize first letter of each word for other fields
      formattedValue = formatProperCase(value);
    }

    setFormData((prev) => ({ ...prev, [placeholder]: formattedValue }));
    // Clear error when user starts typing
    if (formErrors[placeholder]) {
      setFormErrors((prev) => ({ ...prev, [placeholder]: "" }));
    }
  };

  const formatProperCase = (text: string): string => {
    return text
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const formatMiddleInitial = (text: string): string => {
    if (!text.trim()) return "";

    // Take only the first character, capitalize it, and add period if not present
    const initial = text.trim().charAt(0).toUpperCase();
    return initial.endsWith(".") ? initial : initial + ".";
  };

  const formatSuffix = (text: string): string => {
    if (!text.trim()) return "";

    const suffix = text.trim().toUpperCase();

    // Common suffixes that should have periods
    const periodicSuffixes = ["JR", "SR", "DR", "MR", "MS", "MRS"];

    // Roman numerals that don't need periods
    const romanNumerals = [
      "I",
      "II",
      "III",
      "IV",
      "V",
      "VI",
      "VII",
      "VIII",
      "IX",
      "X",
    ];

    // Remove existing periods for processing
    const cleanSuffix = suffix.replace(/\./g, "");

    if (romanNumerals.includes(cleanSuffix)) {
      return cleanSuffix; // Roman numerals without periods
    } else if (periodicSuffixes.includes(cleanSuffix)) {
      return cleanSuffix + "."; // Add period for Jr., Sr., etc.
    } else {
      // For other suffixes, capitalize and add period if it seems appropriate
      return cleanSuffix.endsWith(".") ? cleanSuffix : cleanSuffix + ".";
    }
  };

  const isDatePlaceholder = (placeholder: string): boolean => {
    const datePlaceholders = ["DAY", "MONTH", "YEAR"];

    return datePlaceholders.some((dateKeyword) =>
      placeholder.toUpperCase().includes(dateKeyword)
    );
  };

  const getDateValue = (placeholder: string): string => {
    const now = new Date();
    const placeholderUpper = placeholder.toUpperCase();

    if (placeholderUpper.includes("DAY")) {
      return now.getDate().toString();
    } else if (placeholderUpper.includes("MONTH")) {
      return now.toLocaleDateString("en-US", { month: "long" });
    } else if (placeholderUpper.includes("YEAR")) {
      return now.getFullYear().toString();
    } else {
      // For other date placeholders, return full date
      return now.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        setFormErrors((prev) => ({
          ...prev,
          applicants_photo: "Please select a valid image file",
        }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setFormErrors((prev) => ({
          ...prev,
          applicants_photo: "File size must be less than 5MB",
        }));
        return;
      }

      setFormData((prev) => ({ ...prev, applicants_photo: file }));
      setFormErrors((prev) => ({ ...prev, applicants_photo: "" }));
    }
  };

  const validateForm = (): boolean => {
    const errors: FormErrors = {};

    // Validate required placeholders
    template?.placeholders.forEach((placeholder) => {
      if (!optionalFields.includes(placeholder)) {
        const value = formData[placeholder];
        if (!value || (typeof value === "string" && value.trim() === "")) {
          errors[placeholder] = `${placeholder.replace(/_/g, " ")} is required`;
        }
      }
    });

    // Validate applicant photo if required
    if (hasApplicantPhoto && !formData.applicants_photo) {
      errors.applicants_photo = "Applicant photo is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const generateDocument = async () => {
    if (!template) return;

    setSubmitting(true);
    try {
      toast.info("Generating document...");

      // Get dimensions based on layout size
      const layoutSize = (template.layout_size ||
        "A4") as keyof typeof LAYOUT_DIMENSIONS;
      const dimensions = LAYOUT_DIMENSIONS[layoutSize] || LAYOUT_DIMENSIONS.A4;

      // Create a hidden iframe to load and populate the template
      const iframe = document.createElement("iframe");
      iframe.style.position = "absolute";
      iframe.style.left = "-9999px";
      iframe.style.width = `${dimensions.width}px`;
      iframe.style.height = `${dimensions.height}px`;
      iframe.style.border = "none";

      document.body.appendChild(iframe);

      // Load template HTML in iframe
      const templateUrl = `/api/templates/${template.id}/view`;
      iframe.src = templateUrl;

      // Wait for iframe to load
      await new Promise((resolve, reject) => {
        iframe.onload = resolve;
        iframe.onerror = reject;
        setTimeout(reject, 10000); // 10 second timeout
      });

      // Wait for content to render
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Populate placeholders with form data
      const iframeDoc = iframe.contentDocument!;

      // Replace text placeholders
      template.placeholders.forEach((placeholder) => {
        const value = formData[placeholder] as string;
        if (value) {
          const regex = new RegExp(`\\[${placeholder}\\]`, "g");
          iframeDoc.body.innerHTML = iframeDoc.body.innerHTML.replace(
            regex,
            value
          );
        }
      });

      // Handle applicant photo if present
      if (hasApplicantPhoto && formData.applicants_photo) {
        const photoFile = formData.applicants_photo as File;
        const photoDataUrl = await fileToDataUrl(photoFile);

        // Find and replace the applicant photo
        const photoImg = iframeDoc.querySelector(
          'img[alt*="applicants_photo"]'
        ) as HTMLImageElement;
        if (photoImg) {
          // Get the original image dimensions from the template
          const originalWidth = photoImg.width || photoImg.offsetWidth;
          const originalHeight = photoImg.height || photoImg.offsetHeight;

          // Replace the image source
          photoImg.src = photoDataUrl;

          // Maintain the original size and use object-fit cover for proper cropping
          photoImg.style.width = `${originalWidth}px`;
          photoImg.style.height = `${originalHeight}px`;
          photoImg.style.objectFit = "cover";
          photoImg.style.objectPosition = "center";

          // Ensure the image displays properly
          photoImg.style.display = "block";
        }
      }

      // Wait a bit more for images to load
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Find the main content element or use body
      const contentElement =
        (iframeDoc.querySelector(".ldis-page-wrapper") as HTMLElement) ||
        iframeDoc.body;

      // Capture screenshot using html2canvas
      const canvas = await html2canvas(contentElement, {
        width: dimensions.width,
        height: dimensions.height,
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        x: 0,
        y: 0,
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation:
          dimensions.pdfWidth > dimensions.pdfHeight ? "landscape" : "portrait",
        unit: "mm",
        format: [dimensions.pdfWidth, dimensions.pdfHeight],
      });

      // Convert canvas to image and add to PDF
      const imgData = canvas.toDataURL("image/png");
      pdf.addImage(
        imgData,
        "PNG",
        0,
        0,
        dimensions.pdfWidth,
        dimensions.pdfHeight
      );

      // Generate filename and applicant name
      const fileName = generateFileName(template.template_name);
      const applicantName = generateApplicantName(formData);

      // Create JSON metadata to embed in PDF
      const documentMetadata = await createDocumentMetadata(
        fileName,
        applicantName,
        formData,
        template
      );

      // Embed JSON metadata in PDF as custom properties
      pdf.setProperties({
        title: fileName.replace(".pdf", ""),
        subject: `${template.template_name} - ${applicantName}`,
        author: "Legal Document Issuance System",
        creator: "LDIS",
        keywords: `certificate,legal,document,${template.template_name.toLowerCase()}`,
      });

      // Store metadata in PDF's internal structure and as a text annotation
      // This ensures the JSON metadata is embedded in the PDF file
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (pdf as any).internal.metadata = documentMetadata;

      // Add metadata as an invisible text annotation at the bottom of the page
      // This provides a way to extract the metadata later if needed
      const metadataJson = JSON.stringify(documentMetadata);
      pdf.setFontSize(1); // Very small font size to make it nearly invisible
      pdf.setTextColor(255, 255, 255); // White text (invisible on white background)
      pdf.text(`LDIS_METADATA:${metadataJson}`, 1, dimensions.pdfHeight - 1);

      pdf.save(fileName);

      // Clean up iframe
      document.body.removeChild(iframe);

      toast.success("Document generated and downloaded successfully!");
    } catch (err) {
      console.error("Document generation error:", err);
      toast.error("Failed to generate document. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  const fileToDataUrl = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const generateFileName = (templateName: string): string => {
    // Clean template name (remove underscores, keep spaces)
    const cleanTemplateName = templateName.replace(/_/g, " ");

    // Simply return template name with .pdf extension
    return `${cleanTemplateName}.pdf`;
  };

  const generateApplicantName = (formData: FormData): string => {
    // Extract name components from form data - try different variations
    const lastName =
      (formData.LAST_NAME as string) || (formData["LAST NAME"] as string) || "";
    const firstName =
      (formData.FIRST_NAME as string) ||
      (formData["FIRST NAME"] as string) ||
      "";
    const middleInitial =
      (formData.MIDDLE_INITIAL as string) ||
      (formData["MIDDLE INITIAL"] as string) ||
      (formData["MIDDLE INITIA"] as string) || // Handle typo in medical certificate
      "";
    const suffix = (formData.SUFFIX as string) || "";

    // Build name parts array
    const nameParts = [lastName, firstName];

    // Add middle initial if present
    if (middleInitial.trim()) {
      nameParts.push(middleInitial.trim());
    }

    // Add suffix if present
    if (suffix.trim()) {
      nameParts.push(suffix.trim());
    }

    // Join name parts with commas and spaces
    return nameParts.filter((part) => part.trim()).join(", ");
  };

  const createDocumentMetadata = async (
    documentName: string,
    applicantName: string,
    formData: FormData,
    template: Template
  ) => {
    // Convert applicant photo to base64 if present
    let applicantPhotoBase64 = "";
    if (hasApplicantPhoto && formData.applicants_photo) {
      const photoFile = formData.applicants_photo as File;
      applicantPhotoBase64 = await fileToDataUrl(photoFile);
    }

    // Create document data object with all placeholder values
    const documentData: { [key: string]: string } = {};
    template.placeholders.forEach((placeholder) => {
      const value = formData[placeholder] as string;
      if (value) {
        documentData[placeholder] = value;
      }
    });

    // Add applicant photo to document data if present
    if (applicantPhotoBase64) {
      documentData.applicants_photo = applicantPhotoBase64;
    }

    return {
      document_name: documentName,
      applicant_name: applicantName,
      document_data: documentData,
      generation_info: {
        system: "Legal Document Issuance System",
      },
    };
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      toast.error("Please fill in all required fields");
      return;
    }

    await generateDocument();
  };

  const formatPlaceholderLabel = (placeholder: string): string => {
    return placeholder
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Template not found</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-2 mb-6">
          <FileText className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold">
            Apply for {template.template_name}
          </h1>
        </div>

        {/* Application Form */}
        <div className="bg-card rounded-lg border shadow-sm p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Template Placeholders */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {template.placeholders.map((placeholder) => (
                <div key={placeholder} className="space-y-2">
                  <Label htmlFor={placeholder} className="text-sm font-medium">
                    {formatPlaceholderLabel(placeholder)}
                    {!optionalFields.includes(placeholder) && (
                      <span className="text-red-500 ml-1">*</span>
                    )}
                    {optionalFields.includes(placeholder) && (
                      <span className="text-muted-foreground ml-1">
                        (optional)
                      </span>
                    )}
                  </Label>
                  <Input
                    id={placeholder}
                    type="text"
                    value={(formData[placeholder] as string) || ""}
                    onChange={(e) =>
                      handleInputChange(placeholder, e.target.value)
                    }
                    className={formErrors[placeholder] ? "border-red-500" : ""}
                    placeholder={`Enter ${formatPlaceholderLabel(
                      placeholder
                    ).toLowerCase()}`}
                    disabled={isDatePlaceholder(placeholder)}
                    readOnly={isDatePlaceholder(placeholder)}
                  />
                  {formErrors[placeholder] && (
                    <p className="text-sm text-red-500">
                      {formErrors[placeholder]}
                    </p>
                  )}
                </div>
              ))}
            </div>

            {/* Applicant Photo Upload */}
            {hasApplicantPhoto && (
              <div className="space-y-2">
                <Label
                  htmlFor="applicants_photo"
                  className="text-sm font-medium"
                >
                  Applicant Photo
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {formData.applicants_photo
                      ? "Change Photo"
                      : "Upload Photo"}
                  </Button>
                  {formData.applicants_photo && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <User className="h-4 w-4" />
                      {(formData.applicants_photo as File).name}
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                />
                {formErrors.applicants_photo && (
                  <p className="text-sm text-red-500">
                    {formErrors.applicants_photo}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  Upload a clear photo of the applicant. Max file size: 5MB.
                  Supported formats: JPG, PNG, GIF.
                </p>
              </div>
            )}

            {/* Generate Document Button */}
            <div className="flex justify-end pt-6">
              <Button type="submit" disabled={submitting} className="min-w-40">
                {submitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Generate Document
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
