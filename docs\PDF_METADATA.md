# PDF Metadata System

The Legal Document Issuance System (LDIS) now embeds JSON metadata into generated PDF documents. This feature allows for better document tracking, verification, and data extraction.

## Overview

When a document is generated through the LDIS system, comprehensive metadata is embedded directly into the PDF file. This metadata includes all form data, applicant information, template details, and generation information.

## Metadata Structure

The embedded JSON metadata follows this structure:

```json
{
  "document_name": "good_moral_certificate (<PERSON>, <PERSON>).pdf",
  "applicant_name": "<PERSON><PERSON>, <PERSON>",
  "document_data": {
    "FIRST_NAME": "<PERSON>",
    "LAST_NAME": "Doe",
    "MIDDLE_INITIAL": "A.",
    "SUFFIX": "Jr.",
    "DATE": "January 31, 2025",
    "applicants_photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  },
  "template_info": {
    "id": 1,
    "name": "Good Moral Certificate",
    "description": "Certificate of good moral character",
    "layout_size": "A4"
  },
  "generation_info": {
    "timestamp": "2025-01-31T10:30:00.000Z",
    "system": "Legal Document Issuance System",
    "version": "1.0"
  }
}
```

## Key Components

### Document Name

- Format: `template_name (last_name, first_name middle_initial suffix).pdf`
- Example: `good_moral_certificate (Doe, John A. Jr.).pdf`

### Applicant Name

- Format: `last_name, first_name middle_initial suffix`
- Example: `Doe, John A. Jr.`

### Document Data

- Contains all placeholder values from the form
- Includes applicant photo as base64 encoded data if present
- All field names match the template placeholders

### Template Information

- Template ID, name, description, and layout size
- Allows tracing back to the original template

### Generation Information

- Timestamp of document generation
- System identifier and version
- Useful for auditing and tracking

## How It Works

### 1. Document Generation Process

When a user submits the application form:

1. Form data is validated
2. Template is loaded and populated
3. JSON metadata object is created
4. PDF is generated with embedded metadata

### 2. Metadata Embedding

The metadata is embedded in two ways:

- **Internal PDF Structure**: Stored in the PDF's internal metadata
- **Invisible Text**: Added as white text at the bottom of the document

### 3. Extraction Methods

The metadata can be extracted using:

- PDF text extraction tools
- PDF metadata readers
- Custom parsing utilities

## Implementation Details

### Files Modified

- `src/app/apply/[templateId]/page.tsx` - Main document generation logic
- `src/lib/pdf-metadata-utils.ts` - Utility functions for metadata handling
- `src/components/metadata-demo.tsx` - Demo component for visualization

### Key Functions

- `createDocumentMetadata()` - Creates the metadata object
- `generateApplicantName()` - Formats the applicant name
- `validateDocumentMetadata()` - Validates metadata structure
- `extractMetadataFromPdfText()` - Extracts metadata from PDF text

## Usage Examples

### Viewing Metadata Demo

1. Navigate to Settings page
2. Enable Admin Mode (authentication required)
3. View the "PDF Metadata System" section
4. Explore the metadata structure and examples

### Generating Documents with Metadata

1. Go to any template page
2. Click "Apply for Certificate"
3. Fill out the form completely
4. Click "Generate Document"
5. The downloaded PDF will contain embedded metadata

## Benefits

### Document Verification

- Verify document authenticity
- Check generation timestamp
- Validate applicant information

### Data Extraction

- Extract form data from PDFs
- Automate document processing
- Integrate with other systems

### Audit Trail

- Track document generation
- Monitor system usage
- Maintain compliance records

## Technical Notes

### PDF Library

- Uses jsPDF for PDF generation
- Metadata stored in internal structure
- Invisible text annotation for backup extraction

### Data Security

- Applicant photos stored as base64
- All sensitive data embedded securely
- No external dependencies for metadata

### Browser Compatibility

- Works in all modern browsers
- No additional plugins required
- Client-side generation for privacy

## Future Enhancements

### Potential Improvements

- Digital signatures for metadata
- Encryption for sensitive data
- Batch metadata extraction tools
- API endpoints for metadata access

### Integration Possibilities

- Document management systems
- Verification portals
- Automated processing workflows
- Compliance reporting tools
