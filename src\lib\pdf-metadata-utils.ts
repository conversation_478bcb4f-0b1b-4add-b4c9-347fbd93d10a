/**
 * Utility functions for handling PDF metadata in the LDIS system
 */

export interface DocumentMetadata {
  document_name: string;
  applicant_name: string;
  document_data: {
    [key: string]: string;
  };
  template_info: {
    id: number;
    name: string;
    description?: string;
    layout_size?: string;
  };
  generation_info: {
    timestamp: string;
    system: string;
    version: string;
  };
}

/**
 * Extract JSON metadata from PDF text content
 * This function would be used if you have access to the PDF's text content
 * @param pdfTextContent - The text content extracted from a PDF
 * @returns The parsed metadata object or null if not found
 */
export function extractMetadataFromPdfText(pdfTextContent: string): DocumentMetadata | null {
  try {
    // Look for the LDIS_METADATA marker in the text
    const metadataMatch = pdfTextContent.match(/LDIS_METADATA:({.*})/);
    
    if (metadataMatch && metadataMatch[1]) {
      const metadataJson = metadataMatch[1];
      return JSON.parse(metadataJson) as DocumentMetadata;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting metadata from PDF text:', error);
    return null;
  }
}

/**
 * Validate document metadata structure
 * @param metadata - The metadata object to validate
 * @returns True if the metadata has the expected structure
 */
export function validateDocumentMetadata(metadata: unknown): metadata is DocumentMetadata {
  if (!metadata || typeof metadata !== 'object') {
    return false;
  }

  const obj = metadata as Record<string, unknown>;

  return (
    typeof obj.document_name === 'string' &&
    typeof obj.applicant_name === 'string' &&
    typeof obj.document_data === 'object' &&
    typeof obj.template_info === 'object' &&
    typeof obj.generation_info === 'object' &&
    obj.template_info !== null &&
    obj.generation_info !== null &&
    typeof (obj.template_info as Record<string, unknown>).id === 'number' &&
    typeof (obj.template_info as Record<string, unknown>).name === 'string' &&
    typeof (obj.generation_info as Record<string, unknown>).timestamp === 'string' &&
    typeof (obj.generation_info as Record<string, unknown>).system === 'string' &&
    typeof (obj.generation_info as Record<string, unknown>).version === 'string'
  );
}

/**
 * Format applicant name from metadata
 * @param metadata - The document metadata
 * @returns Formatted applicant name
 */
export function formatApplicantName(metadata: DocumentMetadata): string {
  return metadata.applicant_name || 'Unknown Applicant';
}

/**
 * Get document generation date from metadata
 * @param metadata - The document metadata
 * @returns Formatted date string
 */
export function getDocumentGenerationDate(metadata: DocumentMetadata): string {
  try {
    const date = new Date(metadata.generation_info.timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return 'Unknown Date';
  }
}

/**
 * Extract specific field value from document data
 * @param metadata - The document metadata
 * @param fieldName - The field name to extract
 * @returns The field value or empty string if not found
 */
export function getDocumentField(metadata: DocumentMetadata, fieldName: string): string {
  return metadata.document_data[fieldName] || '';
}

/**
 * Get all available fields from document data
 * @param metadata - The document metadata
 * @returns Array of field names
 */
export function getAvailableFields(metadata: DocumentMetadata): string[] {
  return Object.keys(metadata.document_data);
}

/**
 * Create a summary of the document from metadata
 * @param metadata - The document metadata
 * @returns Document summary object
 */
export function createDocumentSummary(metadata: DocumentMetadata) {
  return {
    documentName: metadata.document_name,
    applicantName: metadata.applicant_name,
    templateName: metadata.template_info.name,
    templateId: metadata.template_info.id,
    generatedDate: getDocumentGenerationDate(metadata),
    system: metadata.generation_info.system,
    version: metadata.generation_info.version,
    fieldCount: Object.keys(metadata.document_data).length,
    hasPhoto: 'applicants_photo' in metadata.document_data
  };
}

/**
 * Example usage and demonstration
 */
export const EXAMPLE_METADATA: DocumentMetadata = {
  document_name: "good_moral_certificate (Doe, John A. Jr.).pdf",
  applicant_name: "Doe, John A. Jr.",
  document_data: {
    FIRST_NAME: "John",
    LAST_NAME: "Doe",
    MIDDLE_INITIAL: "A.",
    SUFFIX: "Jr.",
    DATE: "January 31, 2025",
    applicants_photo: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
  },
  template_info: {
    id: 1,
    name: "Good Moral Certificate",
    description: "Certificate of good moral character",
    layout_size: "A4"
  },
  generation_info: {
    timestamp: "2025-01-31T10:30:00.000Z",
    system: "Legal Document Issuance System",
    version: "1.0"
  }
};
